name: Release and Deploy

on:
  push:
    branches: [main, staging, staging2]
  workflow_dispatch:

jobs:
  env-setup:
    runs-on: blacksmith-2vcpu-ubuntu-2404-arm
    outputs:
      # e.g. production, staging, staging2
      build_env: ${{ steps.set-env.outputs.build_env }}
      # e.g. latest-production, latest-staging, latest-staging2
      image_tag: ${{ steps.set-env.outputs.image_tag }}
      # Docker stack service name
      service_name: ${{ steps.set-env.outputs.service_name }}
      # Docker compose file (e.g. docker-compose.github.production.yml)
      compose_file: ${{ steps.set-env.outputs.compose_file }}

    steps:
      - name: Set environment variables
        id: set-env
        run: |
          # Set build environment based on branch
          if [[ "${{ github.ref_name }}" == "main" ]]; then
            echo "build_env=production" >> $GITHUB_OUTPUT
            echo "service_name=rcom" >> $GITHUB_OUTPUT
            echo "compose_file=docker-compose.github.production.yml" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref_name }}" == "staging2" ]]; then
            echo "build_env=staging2" >> $GITHUB_OUTPUT
            echo "service_name=rcom_staging2" >> $GITHUB_OUTPUT
            echo "compose_file=docker-compose.github.staging2.yml" >> $GITHUB_OUTPUT
          else
            echo "build_env=staging" >> $GITHUB_OUTPUT
            echo "service_name=rcom_staging" >> $GITHUB_OUTPUT
            echo "compose_file=docker-compose.github.staging.yml" >> $GITHUB_OUTPUT
          fi

          # Set image tag based on build environment
          if [[ "${{ github.ref_name }}" == "main" ]]; then
            echo "image_tag=latest-production" >> $GITHUB_OUTPUT
          else
            BRANCH="${{ github.ref_name }}"
            BRANCH_FIXED="${BRANCH//\//-}"
            echo "image_tag=latest-${BRANCH_FIXED}" >> $GITHUB_OUTPUT
          fi
          echo "Environment: $(cat $GITHUB_OUTPUT | grep build_env | cut -d= -f2)"
          echo "Service name: $(cat $GITHUB_OUTPUT | grep service_name | cut -d= -f2)"
          echo "Compose file: $(cat $GITHUB_OUTPUT | grep compose_file | cut -d= -f2)"
          echo "Image tag: $(cat $GITHUB_OUTPUT | grep image_tag | cut -d= -f2)"

  release:
    runs-on: blacksmith-2vcpu-ubuntu-2404-arm
    needs: env-setup
    env:
      DOCKER_BUILDKIT: 1
      BUILDKIT_INLINE_CACHE: 1
      REGISTRY: ghcr.io
    permissions:
      contents: write
      packages: write
      id-token: write
      issues: write
      attestations: write
      pull-requests: write
    strategy:
      fail-fast: true
      matrix:
        config:
          [
            { dockerfile: "flask_app/Dockerfile", image: "rcom" },
            { dockerfile: "fast_api/Dockerfile", image: "web-api" },
          ]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Log in to the Container registry
        uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Build and push
        uses: useblacksmith/build-push-action@v1
        with:
          push: true
          context: .
          file: ${{ matrix.config.dockerfile }}
          build-args: |
            ENVIRONMENT=${{ needs.env-setup.outputs.build_env }}
          tags: |
            ghcr.io/${{ github.repository_owner }}/${{ matrix.config.image }}:${{ needs.env-setup.outputs.image_tag }}
            ghcr.io/${{ github.repository_owner }}/${{ matrix.config.image }}:${{ github.sha }}

  deploy:
    needs: [release, env-setup]
    runs-on: blacksmith-2vcpu-ubuntu-2404-arm

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install SSH client
        run: sudo apt-get update && sudo apt-get install -y openssh-client

      - name: Add SSH key
        run: |
          mkdir -p ~/.ssh/
          echo "${{ secrets.EC2_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 400 ~/.ssh/id_rsa
          ssh-keyscan "${{ secrets.EC2_HOST }}" >> ~/.ssh/known_hosts

      - name: Run commands on EC2
        run: |
          ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }} "
            # Your commands to execute on the EC2 instance go here
            echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin
            cd ~/pipelines/rcom
            source .env
            git fetch --all
            git reset --hard origin/${{ github.ref_name }}
            docker pull ghcr.io/ijack-technologies/rcom:${{ github.sha }} 
            docker pull ghcr.io/ijack-technologies/web-api:${{ github.sha }} 
            export RCOM_IMAGE=ghcr.io/ijack-technologies/rcom:${{ github.sha }} 
            export WEB_API_IMAGE=ghcr.io/ijack-technologies/web-api:${{ github.sha }} 
            docker stack deploy --with-registry-auth --detach=false -c <(docker-compose -f docker-compose.github.base.yml -f ${{ needs.env-setup.outputs.compose_file }} config) ${{ needs.env-setup.outputs.service_name }} & disown
            "
