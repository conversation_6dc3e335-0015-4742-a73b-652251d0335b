import pytest
from httpx import AsyncClient


@pytest.mark.asyncio
async def test_service_cost_overview(client: AsyncClient):
    """Test the service cost overview endpoint"""
    response = await client.post(
        "/v1/service-analytics/cost-overview",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]  # 401 for auth required

    # If auth is bypassed in tests, check response structure
    if response.status_code == 200:
        data = response.json()
        assert "result" in data


@pytest.mark.asyncio
async def test_service_cost_overview_with_selected_years(client: AsyncClient):
    """Test the service cost overview endpoint with selected_years filtering"""
    response = await client.post(
        "/v1/service-analytics/cost-overview",
        json={
            "selected_years": [2024],
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_cost_by_model(client: AsyncClient):
    """Test the cost by model endpoint"""
    response = await client.post(
        "/v1/service-analytics/cost-by-model",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_cost_trends(client: AsyncClient):
    """Test the cost trends endpoint"""
    response = await client.post(
        "/v1/service-analytics/cost-trends",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_high_cost_units(client: AsyncClient):
    """Test the high cost units endpoint"""
    response = await client.post(
        "/v1/service-analytics/high-cost-units",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_high_cost_units_with_selected_years(client: AsyncClient):
    """Test the high cost units endpoint with selected_years filtering"""
    response = await client.post(
        "/v1/service-analytics/high-cost-units",
        json={
            "selected_years": [2024],
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_top_cost_drivers(client: AsyncClient):
    """Test the top cost drivers endpoint"""
    response = await client.post(
        "/v1/service-analytics/top-cost-drivers",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]
