"""
Centralized model registry for React-Admin integration
Eliminates duplicate mappings and provides single source of truth for all admin models
"""

from typing import Dict, List, Optional, Type

from shared.config import ROLE_ID_IJACK_ADMIN
from shared.models import models, models_bom, models_work_order
from app.models import models as fastapi_models

from .generic_crud import GenericCRUDRouter


class ModelConfig:
    """Configuration for a model in the admin interface"""

    def __init__(
        self,
        model_class: Type,
        table_name: str,
        allowed_roles: Optional[List[int]] = None,
        rejected_roles: Optional[List[int]] = None,
        relationships: Optional[List[str]] = None,
        searchable_fields: Optional[List[str]] = None,
        filterable_fields: Optional[List[str]] = None,
        readonly: bool = False,
    ):
        self.model_class = model_class
        self.table_name = table_name
        self.allowed_roles = allowed_roles or [ROLE_ID_IJACK_ADMIN]
        self.rejected_roles = rejected_roles or []
        self.relationships = relationships or []
        self.searchable_fields = searchable_fields or []
        self.filterable_fields = filterable_fields or []
        self.readonly = readonly


# Centralized model registry - single source of truth
MODEL_REGISTRY: Dict[str, ModelConfig] = {
    # User Management & Authentication
    "users": ModelConfig(
        model_class=fastapi_models.User,
        table_name="users",
        relationships=["roles_rel", "customers_rel"],
        searchable_fields=["first_name", "last_name", "email", "phone"],
        filterable_fields=["is_active", "customer_id", "created_at"],
    ),
    "roles": ModelConfig(
        model_class=models.Role,
        table_name="roles",
        searchable_fields=["name", "description"],
        filterable_fields=["is_active"],
    ),
    "user_api_tokens": ModelConfig(
        model_class=models.UserAPIToken,
        table_name="user_api_tokens",
        relationships=["user_rel"],
        searchable_fields=["name"],
        filterable_fields=["user_id", "created_at", "expires_at"],
    ),
    "oauth": ModelConfig(
        model_class=fastapi_models.OAuth,
        table_name="oauth",
        relationships=["user_rel"],
        filterable_fields=["provider", "user_id"],
    ),
    # Customer & Location Management
    "customers": ModelConfig(
        model_class=models.Customer,
        table_name="customers",
        searchable_fields=["name", "email", "phone"],
        filterable_fields=["is_active", "created_at"],
    ),
    "countries": ModelConfig(
        model_class=models.Country,
        table_name="countries",
        searchable_fields=["name", "code"],
        filterable_fields=["is_active"],
    ),
    "provinces": ModelConfig(
        model_class=models.Province,
        table_name="provinces",
        relationships=["country_rel"],
        searchable_fields=["name", "code"],
        filterable_fields=["country_id", "is_active"],
    ),
    "counties": ModelConfig(
        model_class=models.County,
        table_name="counties",
        relationships=["province_rel"],
        searchable_fields=["name"],
        filterable_fields=["province_id"],
    ),
    "cities": ModelConfig(
        model_class=models.City,
        table_name="cities",
        relationships=["province_rel"],
        searchable_fields=["name"],
        filterable_fields=["province_id"],
    ),
    "time_zones": ModelConfig(
        model_class=models.TimeZone,
        table_name="time_zones",
        searchable_fields=["name", "abbreviation"],
        readonly=True,
    ),
    # Equipment & IoT
    "structures": ModelConfig(
        model_class=fastapi_models.Structure,
        table_name="structures",
        relationships=["customer_rel", "gateway_rel"],
        searchable_fields=["name", "location"],
        filterable_fields=["customer_id", "gateway_id", "is_active"],
    ),
    "gateways": ModelConfig(
        model_class=models.Gateway,
        table_name="gateways",
        relationships=["customer_rel", "structures_rel"],
        searchable_fields=["name", "serial_number"],
        filterable_fields=["customer_id", "is_active", "gateway_type"],
    ),
    "gateway_info": ModelConfig(
        model_class=models.GwInfo,
        table_name="gateway_info",
        relationships=["gateway_rel"],
        searchable_fields=["name"],
        filterable_fields=["gateway_id"],
    ),
    "gateway_types": ModelConfig(
        model_class=models.GatewayType,
        table_name="gateway_types",
        searchable_fields=["name", "description"],
    ),
    "power_units": ModelConfig(
        model_class=models.PowerUnit,
        table_name="power_units",
        relationships=["customer_rel"],
        searchable_fields=["name", "serial_number"],
        filterable_fields=["customer_id", "power_unit_type"],
    ),
    "sim_cards": ModelConfig(
        model_class=models.SIMCard,
        table_name="sim_cards",
        searchable_fields=["iccid", "phone_number"],
        filterable_fields=["carrier", "is_active"],
    ),
    # Inventory Management
    "parts": ModelConfig(
        model_class=models_bom.Part,
        table_name="parts",
        relationships=["category_rel"],
        searchable_fields=["name", "part_number", "description"],
        filterable_fields=["category_id", "is_active", "is_serialized"],
    ),
    "part_categories": ModelConfig(
        model_class=models_bom.PartCategory,
        table_name="part_categories",
        searchable_fields=["name", "description"],
        filterable_fields=["is_active"],
    ),
    "warehouses": ModelConfig(
        model_class=models_bom.Warehouse,
        table_name="warehouses",
        searchable_fields=["name", "location"],
        filterable_fields=["is_active"],
    ),
    "warehouse_locations": ModelConfig(
        model_class=models_bom.WarehouseLocation,
        table_name="warehouse_locations",
        relationships=["warehouse_rel"],
        searchable_fields=["name", "barcode"],
        filterable_fields=["warehouse_id", "is_active"],
    ),
    "warehouse_parts": ModelConfig(
        model_class=models_bom.WarehousePart,
        table_name="warehouse_parts",
        relationships=["warehouse_rel", "part_rel", "location_rel"],
        filterable_fields=["warehouse_id", "part_id", "location_id"],
    ),
    "inventory_movements": ModelConfig(
        model_class=models_bom.InventoryMovement,
        table_name="inventory_movements",
        relationships=["warehouse_rel", "part_rel", "user_rel"],
        filterable_fields=["warehouse_id", "part_id", "movement_type", "created_at"],
        readonly=True,  # Audit trail should be read-only
    ),
    "inventory_ledgers": ModelConfig(
        model_class=models_bom.InventoryLedger,
        table_name="inventory_ledgers",
        relationships=["warehouse_rel", "part_rel"],
        filterable_fields=["warehouse_id", "part_id", "operation_type", "created_at"],
        readonly=True,  # Immutable ledger
    ),
    "inventory_reservations": ModelConfig(
        model_class=models_bom.InventoryReservation,
        table_name="inventory_reservations",
        relationships=["warehouse_rel", "part_rel", "user_rel"],
        filterable_fields=["warehouse_id", "part_id", "user_id", "status"],
    ),
    "cycle_counts": ModelConfig(
        model_class=models_bom.CycleCount,
        table_name="cycle_counts",
        relationships=["warehouse_rel", "user_rel"],
        searchable_fields=["name"],
        filterable_fields=["warehouse_id", "status", "created_at"],
    ),
    "cycle_count_items": ModelConfig(
        model_class=models_bom.CycleCountItem,
        table_name="cycle_count_items",
        relationships=["cycle_count_rel", "part_rel"],
        filterable_fields=["cycle_count_id", "part_id", "status"],
    ),
    # Work Orders & Service
    "work_orders": ModelConfig(
        model_class=models_work_order.WorkOrder,
        table_name="work_orders",
        relationships=["customer_rel", "structure_rel", "user_rel"],
        searchable_fields=["title", "description"],
        filterable_fields=["customer_id", "structure_id", "status", "priority"],
    ),
    "work_order_parts": ModelConfig(
        model_class=models_work_order.WorkOrderPart,
        table_name="work_order_parts",
        relationships=["work_order_rel", "part_rel"],
        filterable_fields=["work_order_id", "part_id"],
    ),
    "service_requests": ModelConfig(
        model_class=models_work_order.Service,
        table_name="service_requests",
        relationships=["customer_rel", "structure_rel"],
        searchable_fields=["title", "description"],
        filterable_fields=["customer_id", "structure_id", "status"],
    ),
    "service_clock": ModelConfig(
        model_class=models.ServiceClock,
        table_name="service_clock",
        relationships=["user_rel", "work_order_rel"],
        filterable_fields=["user_id", "work_order_id", "date"],
    ),
    "maintenance": ModelConfig(
        model_class=models_work_order.Maintenance,
        table_name="maintenance",
        relationships=["structure_rel", "maintenance_type_rel"],
        searchable_fields=["description"],
        filterable_fields=["structure_id", "maintenance_type_id", "status"],
    ),
    "maintenance_types": ModelConfig(
        model_class=models_work_order.MaintenanceType,
        table_name="maintenance_types",
        searchable_fields=["name", "description"],
        filterable_fields=["is_active"],
    ),
    # Data & Analytics
    "time_series": ModelConfig(
        model_class=models.TimeSeries,
        table_name="time_series",
        relationships=["gateway_rel"],
        filterable_fields=["gateway_id", "timestamp"],
        readonly=True,
    ),
    "diagnostic_data": ModelConfig(
        model_class=models.Diagnostic,
        table_name="diagnostic_data",
        relationships=["gateway_rel"],
        filterable_fields=["gateway_id", "timestamp"],
        readonly=True,
    ),
    "diagnostic_metrics": ModelConfig(
        model_class=models.DiagnosticMetric,
        table_name="diagnostic_metrics",
        relationships=["gateway_rel"],
        filterable_fields=["gateway_id", "metric_date"],
        readonly=True,
    ),
    "alarm_log_metrics": ModelConfig(
        model_class=models.AlarmLogMetric,
        table_name="alarm_log_metrics",
        relationships=["gateway_rel"],
        filterable_fields=["gateway_id", "metric_date"],
        readonly=True,
    ),
    "error_logs": ModelConfig(
        model_class=models.ErrorLog,
        table_name="error_logs",
        searchable_fields=["message", "traceback"],
        filterable_fields=["level", "created_at"],
        readonly=True,
    ),
    # Alerts & Communications
    "alerts": ModelConfig(
        model_class=models.Alert,
        table_name="alerts",
        relationships=["gateway_rel"],
        searchable_fields=["message"],
        filterable_fields=["gateway_id", "alert_type", "severity", "is_active"],
    ),
    "alerts_sent": ModelConfig(
        model_class=models.AlertsSent,
        table_name="alerts_sent",
        relationships=["alert_rel", "user_rel"],
        filterable_fields=["alert_id", "user_id", "sent_at"],
        readonly=True,
    ),
    "alerts_custom": ModelConfig(
        model_class=models.AlertCustom,
        table_name="alerts_custom",
        relationships=["user_rel", "gateway_rel"],
        searchable_fields=["name", "description"],
        filterable_fields=["user_id", "gateway_id", "is_active"],
    ),
    "alerts_sent_users": ModelConfig(
        model_class=models.AlertsSentUser,
        table_name="alerts_sent_users",
        relationships=["alert_rel", "user_rel"],
        filterable_fields=["alert_id", "user_id", "sent_at"],
        readonly=True,
    ),
    # Forms & Applications
    "applications": ModelConfig(
        model_class=models.Application,
        table_name="applications",
        relationships=["customer_rel"],
        searchable_fields=["name", "description"],
        filterable_fields=["customer_id", "status"],
    ),
    "career_applications": ModelConfig(
        model_class=models.CareerApplication,
        table_name="career_applications",
        searchable_fields=["first_name", "last_name", "email"],
        filterable_fields=["position", "status", "submitted_at"],
    ),
    "contact_form": ModelConfig(
        model_class=models.ContactForm,
        table_name="contact_form",
        searchable_fields=["name", "email", "message"],
        filterable_fields=["submitted_at"],
        readonly=True,
    ),
    "website_views": ModelConfig(
        model_class=models.WebsiteView,
        table_name="website_views",
        filterable_fields=["page_url", "user_id", "timestamp"],
        readonly=True,
    ),
    # System Configuration
    "release_notes": ModelConfig(
        model_class=models.ReleaseNote,
        table_name="release_notes",
        searchable_fields=["title", "description"],
        filterable_fields=["version", "release_date", "is_published"],
    ),
    "calculator": ModelConfig(
        model_class=models.Calculator,
        table_name="calculator",
        searchable_fields=["name", "description"],
        filterable_fields=["is_active"],
    ),
    # BOM & Pricing (subset - add more as needed)
    "model_types": ModelConfig(
        model_class=models_bom.ModelType,
        table_name="model_types",
        searchable_fields=["name", "description"],
        filterable_fields=["is_active"],
    ),
    # Images & Media
    "compression_images": ModelConfig(
        model_class=models.CompressionImage,
        table_name="compression_images",
        relationships=["gateway_rel"],
        filterable_fields=["gateway_id", "created_at"],
    ),
    "surface_images": ModelConfig(
        model_class=models.SurfaceImage,
        table_name="surface_images",
        relationships=["gateway_rel"],
        filterable_fields=["gateway_id", "created_at"],
    ),
    "compression_patterns": ModelConfig(
        model_class=models.CompressionPattern,
        table_name="compression_patterns",
        relationships=["gateway_rel"],
        filterable_fields=["gateway_id", "created_at"],
    ),
    "surface_patterns": ModelConfig(
        model_class=models.SurfacePattern,
        table_name="surface_patterns",
        relationships=["gateway_rel"],
        filterable_fields=["gateway_id", "created_at"],
    ),
    # Additional models can be added here as needed
    "meta_data": ModelConfig(
        model_class=models.MetaDataTbl,
        table_name="meta_data",
        filterable_fields=["data_type", "created_at"],
        readonly=True,
    ),
    "hours": ModelConfig(
        model_class=models.Hour,
        table_name="hours",
        relationships=["user_rel"],
        filterable_fields=["user_id", "date"],
        readonly=True,
    ),
    "images": ModelConfig(
        model_class=models.ImageField,
        table_name="images",
        filterable_fields=["created_at"],
        readonly=True,
    ),
}


def get_model_config(table_name: str) -> Optional[ModelConfig]:
    """Get model configuration by table name"""
    return MODEL_REGISTRY.get(table_name)


def get_all_model_configs() -> Dict[str, ModelConfig]:
    """Get all model configurations"""
    return MODEL_REGISTRY.copy()


def create_crud_router(config: ModelConfig) -> GenericCRUDRouter:
    """Create a CRUD router from model configuration"""
    # Create a basic schema class for the model
    from pydantic import create_model as create_pydantic_model

    # Check if the model has __table__ attribute
    if not hasattr(config.model_class, '__table__'):
        raise AttributeError(
            f"Model {config.model_class.__name__} does not have __table__ attribute. "
            f"This usually means the model is abstract (__abstract__ = True) or not properly defined as a SQLAlchemy model."
        )

    # Get column info from the model
    columns = config.model_class.__table__.columns
    schema_fields = {}

    for column in columns:
        python_type = (
            column.type.python_type if hasattr(column.type, "python_type") else str
        )
        schema_fields[column.name] = (python_type, None)

    # Create dynamic schema
    schema_class = create_pydantic_model(
        f"{config.model_class.__name__}Schema", **schema_fields
    )

    return GenericCRUDRouter(
        model_class=config.model_class,
        schema_class=schema_class,
        table_name=config.table_name,
        allowed_roles=config.allowed_roles,
        rejected_roles=config.rejected_roles,
        relationships=config.relationships,
        searchable_fields=config.searchable_fields,
        filterable_fields=config.filterable_fields,
    )
