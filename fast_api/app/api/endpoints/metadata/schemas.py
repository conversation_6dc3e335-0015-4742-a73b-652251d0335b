from pydantic import BaseModel, ConfigDict, Field


class MetaDataSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    id_cell: str = Field(..., description="Cell identifier")
    element: str = Field(..., description="Element type")
    color: str = Field(..., description="Color value")


class MetaDataCreateSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id_cell: str = Field(..., description="Cell identifier")
    element: str = Field(..., description="Element type")
    color: str = Field(..., description="Color value")