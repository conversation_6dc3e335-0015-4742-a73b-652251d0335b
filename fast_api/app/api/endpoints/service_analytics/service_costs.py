from decimal import Decimal
from typing import List, Optional

from fastapi import APIRouter, Depends, Security
from shared.config import (
    IJACK_CUST_IDS_LIST,
    ROLE_ID_IJACK_SALES,
    ROLE_ID_IJACK_SOFTWARE_DEV,
)
from shared.models.models import Customer, PowerUnit
from shared.models.models_bom import ModelType, Part, Warehouse
from shared.models.models_work_order import (
    ServiceType,
    WorkOrder,
    WorkOrderPart,
    WorkOrderStatus,
    work_order_model_type_rel,
    work_order_user_rel,
)
from sqlalchemy import case, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.models.models import Structure, User
from app.schemas.response import ApiResponse

from .schemas import (
    AvailableYears,
    CostByModel,
    CostDriver,
    CostTrend,
    HighCostUnit,
    ServiceCostFilters,
    ServiceCostOverview,
    StructureWorkOrder,
    WorkOrderPartDetail,
)


def apply_common_filters(query, filters: ServiceCostFilters):
    """Apply common filters to a query that joins WorkOrder, Customer, and WorkOrderPart"""
    # Date filters - selected_years takes precedence over service_dates
    if filters.selected_years is not None and len(filters.selected_years) > 0:
        # Filter by specific years
        query = query.where(func.extract("year", WorkOrder.date_service).in_(filters.selected_years))
    else:
        # Use regular date range filtering if no specific years selected
        if (
            filters.service_dates is not None
            and filters.service_dates.from_date is not None
        ):
            query = query.where(WorkOrder.date_service >= filters.service_dates.from_date)
        if filters.service_dates is not None and filters.service_dates.to_date is not None:
            query = query.where(WorkOrder.date_service <= filters.service_dates.to_date)

    # Customer filter
    if filters.customers is not None and len(filters.customers) > 0:
        query = query.where(Customer.id.in_(filters.customers))

    # Service type filter
    if filters.service_types is not None and len(filters.service_types) > 0:
        query = query.where(WorkOrder.service_type_id.in_(filters.service_types))

    # Technician filter
    if filters.technicians is not None and len(filters.technicians) > 0:
        technician_work_orders = select(work_order_user_rel.c.work_order_id).where(
            work_order_user_rel.c.user_id.in_(filters.technicians)
        )
        query = query.where(WorkOrder.id.in_(technician_work_orders))

    # Part categories filter
    if filters.part_categories is not None and len(filters.part_categories) > 0:
        part_category_conditions = []
        for category in filters.part_categories:
            if category == "050 (Sales)":
                part_category_conditions.append(Part.part_num.like("%050-%"))
            elif category == "060 (PM Parts)":
                part_category_conditions.append(Part.part_num.like("%060-%"))
            elif category == "070 (Service)":
                part_category_conditions.append(Part.part_num.like("%070-%"))
            elif category == "0":
                part_category_conditions.append(Part.part_num == "0")
            elif category == "Other":
                part_category_conditions.append(
                    ~Part.part_num.like("%050-%")
                    & ~Part.part_num.like("%060-%")
                    & ~Part.part_num.like("%070-%")
                    & (Part.part_num != "0")
                )

        if part_category_conditions:
            # We need to join with Part table and filter work orders that have parts in specified categories
            part_filtered_work_orders = (
                select(WorkOrderPart.work_order_id.distinct())
                .select_from(WorkOrderPart)
                .join(Part, WorkOrderPart.part_id == Part.id)
                .where(or_(*part_category_conditions))
            )
            query = query.where(WorkOrder.id.in_(part_filtered_work_orders))

    # IJACK customers filter - when include_ijack is False, exclude IJACK customer IDs
    if filters.include_ijack is False:
        query = query.where(Customer.id.notin_(IJACK_CUST_IDS_LIST))

    # Sales parts filter - when include_sales_parts is False, exclude sales parts (050-)
    if filters.include_sales_parts is False:
        sales_parts_work_orders = (
            select(WorkOrderPart.work_order_id.distinct())
            .select_from(WorkOrderPart)
            .join(Part, WorkOrderPart.part_id == Part.id)
            .where(Part.part_num.like("050-%"))
        )
        query = query.where(WorkOrder.id.notin_(sales_parts_work_orders))

    return query


router = APIRouter(
    tags=["service", "analytics"],
    prefix="/service-analytics",
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
)


@router.post("/cost-overview")
async def get_cost_overview(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[Optional[ServiceCostOverview]]:
    """Get overview of service costs for date range"""
    # Get non-voided work order status IDs
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Build the main query
    query = (
        select(
            func.sum(WorkOrderPart.cost_before_tax).label("total_costs"),
            func.count(WorkOrder.id.distinct()).label("work_order_count"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)  # Add Part join for filtering
        .where(WorkOrder.status_id.in_(status_subquery))
    )

    # Apply part category filtering at the part level
    if filters.part_categories is not None and len(filters.part_categories) > 0:
        part_category_conditions = []
        for category in filters.part_categories:
            if category == "050 (Sales)":
                part_category_conditions.append(Part.part_num.like("050-%"))
            elif category == "060 (PM Parts)":
                part_category_conditions.append(Part.part_num.like("060-%"))
            elif category == "070 (Service)":
                part_category_conditions.append(Part.part_num.like("070-%"))
            elif category == "0":
                part_category_conditions.append(Part.part_num == "0")
            elif category == "Other":
                part_category_conditions.append(
                    ~Part.part_num.like("050-%")
                    & ~Part.part_num.like("060-%")
                    & ~Part.part_num.like("070-%")
                    & (Part.part_num != "0")
                )

        if part_category_conditions:
            query = query.where(or_(*part_category_conditions))

    # Apply other common filters but exclude part categories (we handled them above)
    filters_without_parts = ServiceCostFilters(
        service_dates=filters.service_dates,
        selected_years=filters.selected_years,
        customers=filters.customers,
        service_types=filters.service_types,
        models=filters.models,
        technicians=filters.technicians,
        part_categories=None,  # Already handled above
        structure_id=filters.structure_id,
        work_order_id=filters.work_order_id,
        include_ijack=filters.include_ijack,
        include_sales_parts=filters.include_sales_parts,
    )
    query = apply_common_filters(query, filters_without_parts)

    # Model filtering for cost overview - simplified approach
    if filters.models is not None and len(filters.models) > 0:
        non_null_values = list(filter(lambda x: x is not None, filters.models))
        if len(non_null_values) > 0:
            # Use simple subquery instead of complex EXISTS logic
            model_work_orders = select(work_order_model_type_rel.c.work_order_id).where(
                work_order_model_type_rel.c.model_type_id.in_(non_null_values)
            )
            query = query.where(WorkOrder.id.in_(model_work_orders))

    # Execute query
    response = await db.execute(query)
    result = response.first()

    if not result or result.total_costs is None:
        return {"result": None}

    # Calculate average cost per order
    average_cost_per_order = (
        result.total_costs / result.work_order_count
        if result.work_order_count > 0
        else 0
    )

    # Calculate period comparison (simplified for now)
    # TODO: Add proper period comparison logic
    cost_trend_percentage = 0.0
    period_comparison = "vs last period"

    # For now, we'll set labor/parts costs to half each (TODO: implement proper split)
    labor_costs = result.total_costs / 2
    parts_costs = result.total_costs / 2

    overview = ServiceCostOverview(
        total_costs=result.total_costs,
        labor_costs=labor_costs,
        parts_costs=parts_costs,
        average_cost_per_order=average_cost_per_order,
        work_order_count=result.work_order_count,
        cost_trend_percentage=cost_trend_percentage,
        period_comparison=period_comparison,
    )

    return {"result": overview}


@router.post("/cost-by-model")
async def get_cost_by_model(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[CostByModel]]:
    """Get average yearly service costs per unit grouped by model type with part category breakdown"""

    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # First, get all model types with their costs broken down by category
    base_query = (
        select(
            ModelType.id.label("model_type_id"),
            ModelType.model.label("model_name"),
            WorkOrder.id.label("work_order_id"),
            WorkOrder.date_service,
            WorkOrderPart.structure_id,
            WorkOrderPart.cost_before_tax,
            Part.part_num,
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)
        .join(
            work_order_model_type_rel,
            WorkOrder.id == work_order_model_type_rel.c.work_order_id,
        )
        .join(
            ModelType,
            work_order_model_type_rel.c.model_type_id == ModelType.id,
        )
        .where(WorkOrder.status_id.in_(status_subquery))
    )

    # Apply common filters but exclude part categories (we'll handle that at the part level)
    filters_without_parts = ServiceCostFilters(
        service_dates=filters.service_dates,
        selected_years=filters.selected_years,
        customers=filters.customers,
        service_types=filters.service_types,
        models=filters.models,
        technicians=filters.technicians,
        part_categories=None,  # Don't filter by part categories at work order level
        structure_id=filters.structure_id,
        work_order_id=filters.work_order_id,
        include_ijack=filters.include_ijack,
        include_sales_parts=filters.include_sales_parts,
    )
    base_query = apply_common_filters(base_query, filters_without_parts)

    response = await db.execute(base_query)
    raw_results = response.all()

    # Helper function to check if a part matches the selected categories
    def should_include_part(part_num: str, part_categories: list[str] | None) -> bool:
        if part_categories is None or len(part_categories) == 0:
            return True

        for category in part_categories:
            if category == "050 (Sales)" and part_num.startswith("050-"):
                return True
            elif category == "060 (PM Parts)" and part_num.startswith("060-"):
                return True
            elif category == "070 (Service)" and part_num.startswith("070-"):
                return True
            elif category == "0" and part_num == "0":
                return True
            elif (
                category == "Other"
                and not part_num.startswith("050-")
                and not part_num.startswith("060-")
                and not part_num.startswith("070-")
                and part_num != "0"
            ):
                return True

        return False

    # Process results to calculate costs by model and category
    model_data = {}

    for row in raw_results:
        model_id = row.model_type_id
        model_name = row.model_name

        if model_id not in model_data:
            model_data[model_id] = {
                "model_name": model_name,
                "work_orders": set(),
                "structures": set(),
                "service_dates": [],
                "sales_cost": Decimal("0"),
                "pm_cost": Decimal("0"),
                "labor_cost": Decimal("0"),
                "other_cost": Decimal("0"),
                "total_parts_cost": Decimal("0"),
            }

        data = model_data[model_id]
        data["work_orders"].add(row.work_order_id)
        if row.structure_id:
            data["structures"].add(row.structure_id)
        if row.date_service:
            data["service_dates"].append(row.date_service)

        # Check if this part should be included based on part category filters
        part_num = row.part_num or ""
        if not should_include_part(part_num, filters.part_categories):
            continue

        # Categorize part costs
        part_cost = Decimal(str(row.cost_before_tax or 0))

        if part_num.startswith("050-"):
            data["sales_cost"] += part_cost
        elif part_num.startswith("060-"):
            data["pm_cost"] += part_cost
        elif part_num.startswith("070-"):
            data["labor_cost"] += part_cost  # 070- parts are labor costs
        else:
            data["other_cost"] += part_cost

        data["total_parts_cost"] += part_cost

    # Build final results
    cost_by_model_results = []
    for model_id, data in model_data.items():
        unit_count = len(data["structures"])
        total_cost = data["total_parts_cost"] + data["labor_cost"]

        # Calculate time span in years to get yearly average
        if data["service_dates"]:
            min_date = min(data["service_dates"])
            max_date = max(data["service_dates"])
            days_span = (max_date - min_date).days
            years_span = max(
                days_span / 365.25, 1.0
            )  # Minimum 1 year to avoid division issues
        else:
            years_span = 1.0

        # Calculate average yearly cost per unit
        average_cost_per_unit = (
            (total_cost / unit_count / Decimal(str(years_span)))
            if unit_count > 0
            else Decimal("0")
        )

        cost_by_model_results.append(
            CostByModel(
                model_type_id=model_id,
                model_name=data["model_name"],
                total_cost=total_cost,
                work_order_count=len(data["work_orders"]),
                average_cost_per_unit=average_cost_per_unit,
                unit_count=unit_count,
                sales_cost=data["sales_cost"],
                pm_cost=data["pm_cost"],
                labor_cost=data["labor_cost"],
                other_cost=data["other_cost"],
            )
        )

    # Sort by average cost per unit (descending) and limit to top 10
    cost_by_model_results.sort(key=lambda x: x.average_cost_per_unit, reverse=True)
    cost_by_model_results = cost_by_model_results[:10]

    return {"result": cost_by_model_results}


@router.post("/cost-trends")
async def get_cost_trends(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[CostTrend]]:
    """Get monthly cost trends"""
    # Create the month expression once
    month_expr = func.date_trunc("month", WorkOrder.date_service)

    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    query = (
        select(
            month_expr.label("month"),  # Use the same expression
            func.sum(WorkOrderPart.cost_before_tax).label("total_cost"),
            func.count(WorkOrder.id.distinct()).label("work_order_count"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)  # Add Part join for filtering
        .where(WorkOrder.status_id.in_(status_subquery))
    )

    # Apply part category filtering at the part level
    if filters.part_categories is not None and len(filters.part_categories) > 0:
        part_category_conditions = []
        for category in filters.part_categories:
            if category == "050 (Sales)":
                part_category_conditions.append(Part.part_num.like("050-%"))
            elif category == "060 (PM Parts)":
                part_category_conditions.append(Part.part_num.like("060-%"))
            elif category == "070 (Service)":
                part_category_conditions.append(Part.part_num.like("070-%"))
            elif category == "0":
                part_category_conditions.append(Part.part_num == "0")
            elif category == "Other":
                part_category_conditions.append(
                    ~Part.part_num.like("050-%")
                    & ~Part.part_num.like("060-%")
                    & ~Part.part_num.like("070-%")
                    & (Part.part_num != "0")
                )

        if part_category_conditions:
            query = query.where(or_(*part_category_conditions))

    # Apply other common filters but exclude part categories (we handled them above)
    filters_without_parts = ServiceCostFilters(
        service_dates=filters.service_dates,
        selected_years=filters.selected_years,
        customers=filters.customers,
        service_types=filters.service_types,
        models=filters.models,
        technicians=filters.technicians,
        part_categories=None,  # Already handled above
        structure_id=filters.structure_id,
        work_order_id=filters.work_order_id,
        include_ijack=filters.include_ijack,
        include_sales_parts=filters.include_sales_parts,
    )
    query = apply_common_filters(query, filters_without_parts)

    # Model filtering for cost trends - simplified approach
    if filters.models is not None and len(filters.models) > 0:
        non_null_values = list(filter(lambda x: x is not None, filters.models))
        if len(non_null_values) > 0:
            # Use simple subquery instead of complex EXISTS logic
            model_work_orders = select(work_order_model_type_rel.c.work_order_id).where(
                work_order_model_type_rel.c.model_type_id.in_(non_null_values)
            )
            query = query.where(WorkOrder.id.in_(model_work_orders))

    # Use the same expression here
    query = query.group_by(month_expr).order_by(month_expr)

    response = await db.execute(query)
    results = response.all()

    # Convert to schema objects
    cost_trends = []
    for result in results:
        # TODO: Split labor and parts costs properly
        labor_cost = result.total_cost / 2
        parts_cost = result.total_cost / 2

        cost_trends.append(
            CostTrend(
                month=result.month.date() if result.month else None,
                total_cost=result.total_cost,
                labor_cost=labor_cost,
                parts_cost=parts_cost,
                work_order_count=result.work_order_count,
            )
        )

    return {"result": cost_trends}


@router.post("/high-cost-units")
async def get_high_cost_units(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[HighCostUnit]]:
    """Get units with highest service costs"""
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    query = (
        select(
            WorkOrderPart.structure_id,
            Structure.structure_str.label("structure_number"),
            PowerUnit.power_unit_str.label("power_unit"),
            ModelType.model.label("model_name"),
            Customer.customer.label("customer_name"),
            func.sum(WorkOrderPart.cost_before_tax).label("total_cost"),
            func.count(WorkOrder.id.distinct()).label("work_order_count"),
            func.max(WorkOrder.date_service).label("last_service_date"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)  # Add Part join for filtering
        .join(Structure, WorkOrderPart.structure_id == Structure.id, isouter=True)
        .join(ModelType, Structure.model_type_id == ModelType.id, isouter=True)
        .join(PowerUnit, Structure.power_unit_id == PowerUnit.id, isouter=True)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id.is_not(None),
        )
    )

    # Apply part category filtering at the part level
    if filters.part_categories is not None and len(filters.part_categories) > 0:
        part_category_conditions = []
        for category in filters.part_categories:
            if category == "050 (Sales)":
                part_category_conditions.append(Part.part_num.like("050-%"))
            elif category == "060 (PM Parts)":
                part_category_conditions.append(Part.part_num.like("060-%"))
            elif category == "070 (Service)":
                part_category_conditions.append(Part.part_num.like("070-%"))
            elif category == "0":
                part_category_conditions.append(Part.part_num == "0")
            elif category == "Other":
                part_category_conditions.append(
                    ~Part.part_num.like("050-%")
                    & ~Part.part_num.like("060-%")
                    & ~Part.part_num.like("070-%")
                    & (Part.part_num != "0")
                )

        if part_category_conditions:
            query = query.where(or_(*part_category_conditions))

    # Apply other common filters but exclude part categories (we handled them above)
    filters_without_parts = ServiceCostFilters(
        service_dates=filters.service_dates,
        selected_years=filters.selected_years,
        customers=filters.customers,
        service_types=filters.service_types,
        models=filters.models,
        technicians=filters.technicians,
        part_categories=None,  # Already handled above
        structure_id=filters.structure_id,
        work_order_id=filters.work_order_id,
        include_ijack=filters.include_ijack,
        include_sales_parts=filters.include_sales_parts,
    )
    query = apply_common_filters(query, filters_without_parts)

    # Model filtering for high-cost units - simplified approach
    if filters.models is not None and len(filters.models) > 0:
        non_null_values = list(filter(lambda x: x is not None, filters.models))
        if len(non_null_values) > 0:
            query = query.where(ModelType.id.in_(non_null_values))

    query = (
        query.group_by(
            WorkOrderPart.structure_id,
            Structure.structure_str,
            PowerUnit.power_unit_str,
            ModelType.model,
            Customer.customer,
        )
        .order_by(func.sum(WorkOrderPart.cost_before_tax).desc())
        .limit(50)
    )

    response = await db.execute(query)
    results = response.all()

    # Convert to schema objects
    high_cost_units = []
    for result in results:
        avg_cost_per_service = (
            result.total_cost / result.work_order_count
            if result.work_order_count > 0
            else 0
        )

        # TODO: Calculate actual cost trend
        cost_trend = "stable"

        high_cost_units.append(
            HighCostUnit(
                structure_id=result.structure_id,
                structure_number=result.structure_number,
                power_unit=result.power_unit,
                model_name=result.model_name,
                customer_name=result.customer_name,
                total_cost=result.total_cost,
                work_order_count=result.work_order_count,
                avg_cost_per_service=avg_cost_per_service,
                last_service_date=result.last_service_date,
                cost_trend=cost_trend,
            )
        )

    return {"result": high_cost_units}


@router.post("/top-cost-drivers")
async def get_top_cost_drivers(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[CostDriver]]:
    """Get top cost drivers across different categories"""
    # For now, return model-based cost drivers
    # TODO: Expand to include parts, service types, etc.

    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Calculate total costs for percentage calculation
    total_query = (
        select(func.sum(WorkOrderPart.cost_before_tax))
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .where(WorkOrder.status_id.in_(status_subquery))
    )

    # Apply same filters to total query
    total_query = apply_common_filters(total_query, filters)

    total_response = await db.execute(total_query)
    total_costs = total_response.scalar() or Decimal(0)

    # Get model-based cost drivers
    model_query = (
        select(
            ModelType.model.label("name"),
            func.sum(WorkOrderPart.cost_before_tax).label("total_cost"),
            func.count(WorkOrder.id.distinct()).label("work_order_count"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(
            work_order_model_type_rel,
            WorkOrder.id == work_order_model_type_rel.c.work_order_id,
        )
        .join(
            ModelType,
            work_order_model_type_rel.c.model_type_id == ModelType.id,
        )
        .where(WorkOrder.status_id.in_(status_subquery))
    )

    # Apply same filters
    model_query = apply_common_filters(model_query, filters)

    model_query = (
        model_query.group_by(ModelType.model)
        .order_by(func.sum(WorkOrderPart.cost_before_tax).desc())
        .limit(5)
    )

    response = await db.execute(model_query)
    results = response.all()

    cost_drivers = []
    for result in results:
        percentage = (
            float(result.total_cost / total_costs * 100) if total_costs > 0 else 0
        )

        cost_drivers.append(
            CostDriver(
                category="model",
                name=result.name,
                total_cost=result.total_cost,
                percentage=percentage,
                work_order_count=result.work_order_count,
            )
        )

    return {"result": cost_drivers}


@router.post("/structure-work-orders")
async def get_structure_work_orders(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[StructureWorkOrder]]:
    """Get all work orders for a specific structure"""

    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Calculate labor hours and costs from 070 (Service) parts
    labor_subquery = (
        select(
            WorkOrder.id.label("wo_id"),
            func.sum(WorkOrderPart.quantity).label("labor_hours"),
            func.sum(WorkOrderPart.cost_before_tax).label("labor_cost"),
        )
        .select_from(WorkOrder)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id == filters.structure_id,
            Part.part_num.like("%070-%"),  # Service labor parts
        )
        .group_by(WorkOrder.id)
    ).alias("labor_data")

    # Calculate non-labor parts costs
    parts_subquery = (
        select(
            WorkOrder.id.label("wo_id"),
            func.sum(WorkOrderPart.cost_before_tax).label("parts_cost"),
        )
        .select_from(WorkOrder)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id == filters.structure_id,
            ~Part.part_num.like("%070-%"),  # Non-service parts
        )
        .group_by(WorkOrder.id)
    ).alias("parts_data")

    # Get technician names for work orders (can be multiple)
    from shared.models.models_work_order import work_order_user_rel

    technician_subquery = (
        select(
            work_order_user_rel.c.work_order_id.label("wo_id"),
            func.string_agg(User.full_name, ", ").label("technician_names"),
        )
        .select_from(work_order_user_rel)
        .join(User, work_order_user_rel.c.user_id == User.id)
        .group_by(work_order_user_rel.c.work_order_id)
    ).alias("technician_data")

    query = (
        select(
            WorkOrder.id.label("work_order_id"),
            WorkOrder.date_service,
            ServiceType.name.label("service_type"),
            func.coalesce(technician_subquery.c.technician_names, "Unassigned").label(
                "technician_name"
            ),
            func.coalesce(labor_subquery.c.labor_hours, 0).label("labor_hours"),
            func.coalesce(labor_subquery.c.labor_cost, 0).label("labor_cost"),
            func.coalesce(parts_subquery.c.parts_cost, 0).label("parts_cost"),
            (
                func.coalesce(labor_subquery.c.labor_cost, 0)
                + func.coalesce(parts_subquery.c.parts_cost, 0)
            ).label("total_cost"),
            WorkOrderStatus.name.label("status"),
            WorkOrder.work_done.label("work_description"),
        )
        .select_from(WorkOrder)
        .join(
            Customer, WorkOrder.customer_id == Customer.id
        )  # Add Customer join for common filters
        .join(WorkOrderStatus, WorkOrder.status_id == WorkOrderStatus.id, isouter=True)
        .join(ServiceType, WorkOrder.service_type_id == ServiceType.id, isouter=True)
        .join(
            technician_subquery,
            WorkOrder.id == technician_subquery.c.wo_id,
            isouter=True,
        )
        .join(labor_subquery, WorkOrder.id == labor_subquery.c.wo_id, isouter=True)
        .join(parts_subquery, WorkOrder.id == parts_subquery.c.wo_id, isouter=True)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            # Only include work orders that have parts for this structure
            WorkOrder.id.in_(
                select(WorkOrderPart.work_order_id).where(
                    WorkOrderPart.structure_id == filters.structure_id
                )
            ),
        )
        .order_by(WorkOrder.date_service.desc())
    )

    # Apply common filters (including date filters and Include IJACK filter)
    query = apply_common_filters(query, filters)

    response = await db.execute(query)
    results = response.all()

    structure_work_orders = []
    for result in results:
        structure_work_orders.append(
            StructureWorkOrder(
                work_order_id=result.work_order_id,
                date_service=result.date_service,
                service_type=result.service_type,
                technician_name=result.technician_name,
                labor_hours=float(result.labor_hours or 0),
                labor_cost=result.labor_cost or Decimal(0),
                parts_cost=result.parts_cost or Decimal(0),
                total_cost=result.total_cost or Decimal(0),
                status=result.status,
                work_description=result.work_description,
            )
        )

    return {"result": structure_work_orders}


@router.post("/work-order-parts")
async def get_work_order_parts(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[WorkOrderPartDetail]]:
    """Get all parts and costs for a specific work order"""

    query = (
        select(
            WorkOrderPart.id.label("part_id"),
            Part.part_name,
            Part.description,
            WorkOrderPart.quantity,
            WorkOrderPart.price,
            WorkOrderPart.cost_before_tax.label("total_cost"),
            Warehouse.name.label("warehouse"),
            case(
                (Part.part_num.like("%050-%"), "050 (Sales)"),
                (Part.part_num.like("%060-%"), "060 (PM Parts)"),
                (Part.part_num.like("%070-%"), "070 (Service)"),
                (Part.part_num == "0", "0"),
                else_="Other",
            ).label("category"),
        )
        .select_from(WorkOrderPart)
        .join(Part, WorkOrderPart.part_id == Part.id, isouter=True)
        .join(Warehouse, WorkOrderPart.warehouse_id == Warehouse.id, isouter=True)
        .where(WorkOrderPart.work_order_id == filters.work_order_id)
        .order_by(WorkOrderPart.cost_before_tax.desc())
    )

    response = await db.execute(query)
    results = response.all()

    work_order_parts = []
    for result in results:
        work_order_parts.append(
            WorkOrderPartDetail(
                part_id=result.part_id,
                part_name=result.part_name,
                description=result.description,
                quantity=result.quantity or 0,
                price=result.price or Decimal(0),
                total_cost=result.total_cost or Decimal(0),
                warehouse=result.warehouse,
                category=result.category,
            )
        )

    return {"result": work_order_parts}


@router.post("/available-years")
async def get_available_years(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[AvailableYears]:
    """Get all available years with work order data, respecting filters except date filters"""
    
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Build query to get distinct years
    query = (
        select(func.extract("year", WorkOrder.date_service).distinct().label("year"))
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrder.date_service.is_not(None)
        )
    )

    # Apply part category filtering at the part level
    if filters.part_categories is not None and len(filters.part_categories) > 0:
        part_category_conditions = []
        for category in filters.part_categories:
            if category == "050 (Sales)":
                part_category_conditions.append(Part.part_num.like("050-%"))
            elif category == "060 (PM Parts)":
                part_category_conditions.append(Part.part_num.like("060-%"))
            elif category == "070 (Service)":
                part_category_conditions.append(Part.part_num.like("070-%"))
            elif category == "0":
                part_category_conditions.append(Part.part_num == "0")
            elif category == "Other":
                part_category_conditions.append(
                    ~Part.part_num.like("050-%") &
                    ~Part.part_num.like("060-%") &
                    ~Part.part_num.like("070-%") &
                    (Part.part_num != "0")
                )
        
        if part_category_conditions:
            query = query.where(or_(*part_category_conditions))

    # Apply other common filters but exclude date and year filters (we want all years)
    filters_without_dates = ServiceCostFilters(
        service_dates=None,  # Don't filter by dates - we want all years
        selected_years=None,  # Don't filter by years - we want all years
        customers=filters.customers,
        service_types=filters.service_types,
        models=filters.models,
        technicians=filters.technicians,
        part_categories=None,  # Already handled above
        structure_id=filters.structure_id,
        work_order_id=filters.work_order_id,
        include_ijack=filters.include_ijack,
        include_sales_parts=filters.include_sales_parts
    )
    query = apply_common_filters(query, filters_without_dates)

    # Model filtering - simplified approach
    if filters.models is not None and len(filters.models) > 0:
        non_null_values = list(filter(lambda x: x is not None, filters.models))
        if len(non_null_values) > 0:
            model_work_orders = select(work_order_model_type_rel.c.work_order_id).where(
                work_order_model_type_rel.c.model_type_id.in_(non_null_values)
            )
            query = query.where(WorkOrder.id.in_(model_work_orders))

    # Order by year descending
    query = query.order_by(func.extract("year", WorkOrder.date_service).desc())

    response = await db.execute(query)
    results = response.all()

    # Extract years and convert to integers
    years = [int(result.year) for result in results if result.year is not None]

    return {"result": AvailableYears(years=years)}
