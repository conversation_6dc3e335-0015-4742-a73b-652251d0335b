from datetime import date
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field


class DateRange(BaseModel):
    from_date: Optional[date] = Field(None, alias="from")
    to_date: Optional[date] = Field(None, alias="to")


class ServiceCostFilters(BaseModel):
    service_dates: Optional[DateRange] = None
    selected_years: Optional[list[int]] = Field(default=None, description="Filter by specific years (takes precedence over service_dates)")
    customers: Optional[list[int]] = None
    service_types: Optional[list[int]] = None
    models: Optional[list[int | None]] = None
    technicians: Optional[list[int]] = None
    part_categories: Optional[list[str]] = None
    structure_id: Optional[int] = None
    work_order_id: Optional[int] = None
    include_ijack: Optional[bool] = Field(default=False, description="Include IJACK customer IDs (1, 3) in results")
    include_sales_parts: Optional[bool] = Field(default=False, description="Include sales parts (050-) in service cost analysis")


class ServiceCostOverview(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    total_costs: Decimal = Field(..., description="Total service costs")
    labor_costs: Decimal = Field(..., description="Total labor costs")
    parts_costs: Decimal = Field(..., description="Total parts costs")
    average_cost_per_order: Decimal
    work_order_count: int
    cost_trend_percentage: float
    period_comparison: str = Field(..., description="vs last period")


class CostByModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    model_type_id: int
    model_name: str
    total_cost: Decimal
    work_order_count: int
    average_cost_per_unit: Decimal  # Changed from average_cost for clarity
    unit_count: int  # Number of unique units/structures for this model
    # Part category breakdown
    sales_cost: Decimal = Field(default=0, description="050 Sales parts cost")
    pm_cost: Decimal = Field(default=0, description="060 PM parts cost") 
    labor_cost: Decimal = Field(default=0, description="070 Labor/Service parts cost")
    other_cost: Decimal = Field(default=0, description="Other parts cost")


class TechnicianPerformance(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    technician_id: int
    technician_name: str
    total_orders: int
    total_cost: Decimal
    average_order_cost: Decimal
    efficiency_score: float


class HighCostUnit(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    power_unit: Optional[str]
    model_name: Optional[str]
    customer_name: Optional[str]
    total_cost: Decimal
    work_order_count: int
    avg_cost_per_service: Decimal
    last_service_date: Optional[date]
    cost_trend: str


class CostTrend(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    month: date
    total_cost: Decimal
    labor_cost: Decimal
    parts_cost: Decimal
    work_order_count: int


class CostDriver(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    category: str  # 'model', 'unit', 'part', 'service_type'
    name: str
    total_cost: Decimal
    percentage: float
    work_order_count: int


class StructureWorkOrder(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    work_order_id: int
    date_service: Optional[date]
    service_type: Optional[str]
    technician_name: Optional[str]
    labor_hours: Optional[float]
    labor_cost: Decimal
    parts_cost: Decimal
    total_cost: Decimal
    status: Optional[str]
    work_description: Optional[str]


class WorkOrderPartDetail(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    part_id: int
    part_name: Optional[str]
    description: Optional[str]
    quantity: float
    price: Decimal
    total_cost: Decimal
    warehouse: Optional[str]
    category: Optional[str]


class AvailableYears(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    years: list[int] = Field(..., description="List of available years with work order data, sorted descending")
