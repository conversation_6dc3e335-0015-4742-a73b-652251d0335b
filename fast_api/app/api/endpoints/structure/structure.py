from typing import List

import pandas as pd
from fastapi import APIRouter, Depends, HTTPException, Security
from redis.asyncio import Redis
from shared.config import ROLE_ID_IJACK_ADMIN
from shared.models.models import StructureVw
from sqlalchemy import and_, select, text
from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.db.redis import cache_memoize, get_async_redis
from app.models.models import Structure
from shared.models.models import PowerUnit
from app.schemas.response import ApiResponse

from .schemas import StructuresByYearAndUnitTypeSchema, StructureSchema

router = APIRouter()


@cache_memoize(timeout=300)  # Cache for 5 minutes
async def get_structures_cached(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> pd.DataFrame:
    """
    Get the StructureVw model with caching.
    This function is used to cache the result of the query for performance.

    Parameters:
    -----------
    ijack_db : AsyncSession
        Database session for executing queries
    redis_client : Redis
        Redis client for caching

    Returns:
    --------
    pd.DataFrame
        DataFrame of StructureVw records
    """
    # Select all columns from the StructureVw model
    # and filter out unwanted records
    query = select(
        StructureVw.id,
        StructureVw.structure_id,
        StructureVw.structure,
        StructureVw.structure_str,
        StructureVw.unit_type_id,
        StructureVw.unit_type,
        StructureVw.model_type_id,
        StructureVw.model,
        StructureVw.customer_id,
        StructureVw.customer,
        StructureVw.structure_install_date,
    ).where(
        and_(
            StructureVw.structure_install_date.is_not(None),
            StructureVw.unit_type.is_not(None),
            StructureVw.customer_id.is_not(None),
        )
    )

    result = await ijack_db.execute(query)
    records = result.all()

    # Convert to DataFrame for easier manipulation
    df = pd.DataFrame(records)
    return df


@router.get(
    "/by-year-by-unit-type",
)
async def get_structures_by_year_by_unit_type(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> ApiResponse[List[StructuresByYearAndUnitTypeSchema]]:
    """
    Get the count of structures grouped by year and unit type.

    Parameters:
    -----------
    ijack_db : AsyncSession
        Database session for executing queries
    redis_client : Redis
        Redis client for caching

    Returns:
    --------
    ApiResponse
        Response with structure counts by year and unit type
    """
    # Get structures from cache
    df = await get_structures_cached(ijack_db, redis_client)

    # Extract year from structure_install_date
    df["year"] = pd.to_datetime(df["structure_install_date"]).dt.year

    # Group by year and unit_type and count
    grouped = df.groupby(["year", "unit_type"]).size().reset_index(name="total_count")

    # Filter out any rows with NaN values
    grouped = grouped.dropna()

    # Convert to list of dicts for response
    result = grouped.to_dict("records")

    return {"result": result}


# CRUD endpoints migrated from Flask structures API
@router.get("/", response_model=ApiResponse[List[StructureSchema]])
async def get_all_structures(ijack_db: AsyncSession = Depends(get_ijack_db)):
    """
    Get all structures - migrated from Flask structures API
    Returns a list of all Structure records
    """
    query = select(Structure)
    result = await ijack_db.execute(query)
    structures = result.scalars().all()

    # Convert to dict for JSON serialization
    data = []
    for structure in structures:
        structure_dict = {
            "id": structure.id,
            "structure": str(structure.structure) if structure.structure else None,
        }
        data.append(structure_dict)

    return {"result": data}


@router.get("/{structure_id}", response_model=ApiResponse[StructureSchema])
async def get_structure_by_id(
    structure_id: str, ijack_db: AsyncSession = Depends(get_ijack_db)
):
    """
    Get structure by ID - migrated from Flask structures API
    Returns a single Structure record based on the structure ID
    """
    try:
        structure_id_int = int(structure_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid structure ID format")
    
    # Query with left join to get power unit string
    query = select(
        Structure.id,
        Structure.structure,
        Structure.structure_str,
        Structure.unit_type_id,
        Structure.model_type_id,
        Structure.structure_install_date,
        PowerUnit.power_unit_str
    ).select_from(
        Structure.__table__.outerjoin(PowerUnit.__table__, Structure.power_unit_id == PowerUnit.id)
    ).where(Structure.id == structure_id_int)
    
    result = await ijack_db.execute(query)
    row = result.fetchone()

    if not row:
        raise HTTPException(status_code=404, detail="Structure not found")

    structure_dict = {
        "id": row[0],
        "structure_id": row[0],  # Use id as structure_id for schema compatibility
        "structure": float(row[1]) if row[1] else None,
        "structure_str": row[2],
        "unit_type_id": row[3],
        "unit_type": None,  # Would need to join with unit_types table
        "model_type_id": row[4],
        "model": None,  # Would need to join with model_types table
        "customer_id": None,  # Would need to join with customers table
        "customer": None,  # Would need to join with customers table
        "structure_install_date": row[5].isoformat() if row[5] else None,
        "power_unit_str": row[6],  # Add power unit string
    }

    return {"result": structure_dict}


@router.get(
    "/by_customer_id/{customer_id}", response_model=ApiResponse[List[StructureSchema]]
)
async def get_structures_by_customer_id(
    customer_id: str, ijack_db: AsyncSession = Depends(get_ijack_db)
):
    """
    Get structures by customer ID - migrated from Flask structures API
    Returns a list of all structures for a given customer ID
    """
    if customer_id:
        # Try to convert to int first, fallback to string comparison for non-numeric IDs
        try:
            customer_id_int = int(customer_id)
            query = (
                select(StructureVw)
                .where(StructureVw.customer_id == customer_id_int)
                .order_by(StructureVw.structure)
            )
        except ValueError:
            query = (
                select(StructureVw)
                .where(StructureVw.customer_id == customer_id.upper())
                .order_by(StructureVw.structure)
            )
        result = await ijack_db.execute(query)
        structures = result.scalars().all()

        if not structures:
            raise HTTPException(
                status_code=404,
                detail=f"No records for that customer ID '{customer_id}'",
            )

        # Convert to dict for JSON serialization
        data = []
        for structure in structures:
            structure_dict = {
                "id": structure.id,
                "structure_id": structure.structure_id,
                "structure": structure.structure,
                "structure_str": structure.structure_str,
                "unit_type_id": structure.unit_type_id,
                "unit_type": structure.unit_type,
                "model_type_id": structure.model_type_id,
                "model": structure.model,
                "customer_id": structure.customer_id,
                "customer": structure.customer,
                "structure_install_date": structure.structure_install_date.isoformat()
                if structure.structure_install_date
                else None,
            }
            data.append(structure_dict)

        return {"result": data}
    else:
        # Return all structures if no customer_id provided
        query = select(StructureVw)
        result = await ijack_db.execute(query)
        structures = result.scalars().all()

        data = []
        for structure in structures:
            structure_dict = {
                "id": structure.id,
                "structure_id": structure.structure_id,
                "structure": structure.structure,
                "structure_str": structure.structure_str,
                "unit_type_id": structure.unit_type_id,
                "unit_type": structure.unit_type,
                "model_type_id": structure.model_type_id,
                "model": structure.model,
                "customer_id": structure.customer_id,
                "customer": structure.customer,
                "structure_install_date": structure.structure_install_date.isoformat()
                if structure.structure_install_date
                else None,
            }
            data.append(structure_dict)

        return {"result": data}


@router.get("/by_gateway/{gateway}", response_model=ApiResponse[StructureSchema])
async def get_structure_by_gateway(
    gateway: str,
    ijack_db: AsyncSession = Depends(get_ijack_db),
    _: int = Security(roles_required([ROLE_ID_IJACK_ADMIN])),
):
    """
    Get structure by gateway - migrated from Flask structures API
    Returns a single structure record based on the gateway (admin only)
    """
    # Raw SQL query as in Flask version
    sql = text("""
        SELECT time_zone_id
        FROM public.vw_structures_joined_filtered
        WHERE gateway = :gw
            AND unit_type_id IS NOT NULL
            AND customer_id IS DISTINCT FROM 21 -- demo customer
        LIMIT 1
    """).bindparams(gw=gateway)

    result = await ijack_db.execute(sql)
    row = result.fetchone()

    if not row:
        raise HTTPException(status_code=404, detail="Structure not found")

    time_zone_id = row[0]

    # Get structure by time_zone_id
    query = select(Structure).where(Structure.id == time_zone_id)
    result = await ijack_db.execute(query)
    structure = result.scalar_one_or_none()

    if not structure:
        raise HTTPException(status_code=404, detail="Structure not found")

    structure_dict = {
        "id": structure.id,
        "structure": str(structure.structure) if structure.structure else None,
    }

    return {"result": structure_dict}
