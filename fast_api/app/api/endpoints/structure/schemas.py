from typing import Optional

from pydantic import BaseModel, ConfigDict, Field


class ModelSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    id: int
    model: str = Field(..., description="The name of the model")
    unit_type: str = Field(
        ..., description="The product family that the model belongs to"
    )


class StructureSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    structure_id: int
    structure: Optional[float] = None
    structure_str: Optional[str] = None
    unit_type_id: Optional[int] = None
    unit_type: Optional[str] = None
    model_type_id: Optional[int] = None
    model: Optional[str] = None
    customer_id: Optional[int] = None
    customer: Optional[str] = None
    structure_install_date: Optional[str] = None
    power_unit_str: Optional[str] = None


class StructuresByYearAndUnitTypeSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    year: int
    unit_type: str
    total_count: int
