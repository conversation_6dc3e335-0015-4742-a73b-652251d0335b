import asyncio

import msgpack
from fastapi import Depends, Request
from redis.asyncio import Redis

# Import all model modules to ensure they're registered with the metadata
# noqa
# from shared.models.models import *
from shared.models.models import user_customer_rel, user_role_rel

# from shared.models.models_bom import *
# from shared.models.models_work_order import *
from sqlalchemy import select
from sqlalchemy.orm import Session

from app.api.exceptions.auth import ForbiddenException, UnauthorizedException
from app.core.config import settings
from app.core.logging import logger
from app.db.database import get_ijack_db
from app.db.redis import get_async_redis
from app.models.models import User


async def get_user_id(
    request: Request,
    redis: Redis = Depends(get_async_redis),
):
    """
    Get the user ID from the session cookie.
    :param request: FastAPI request object.
    :param redis: Redis client.
    :return: User ID.
    :raises UnauthorizedException: If the user is not authenticated.
    """
    logger.debug("Checking user ID from session cookie.")

    session = request.cookies.get(settings.SESSION_COOKIE_NAME)
    if not session:
        logger.error("No session cookie found.")
        raise UnauthorizedException()

    # Retry logic to handle Flask session storage timing
    max_retries = 3
    base_delay = 0.5  # 50ms base delay

    for attempt in range(max_retries):
        try:
            result = await redis.get(f"session:{session}")

            if result is None:
                if attempt < max_retries - 1:
                    # Exponential backoff: 50ms, 100ms, 200ms
                    delay = base_delay * (2**attempt)
                    logger.warning(
                        f"Session not found in Redis, retrying in {delay}s (attempt {attempt + 1}/{max_retries})"
                    )
                    await asyncio.sleep(delay)
                    continue
                else:
                    logger.error("Session not found in Redis after all retries.")
                    raise UnauthorizedException()

            # Session found, decode and extract user_id
            auth = msgpack.unpackb(result)
            user_id = auth.get("_user_id")

            if not user_id:
                logger.error("User ID not found in session data.")
                raise UnauthorizedException()

            logger.debug(f"Successfully retrieved user_id: {user_id}")
            return int(user_id)

        except msgpack.exceptions.ExtraData as e:
            logger.error(f"Failed to decode session data: {e}")
            raise UnauthorizedException()
        except Exception as e:
            logger.error(f"Unexpected error retrieving session: {str(e)}")
            if attempt == max_retries - 1:
                raise UnauthorizedException()
            await asyncio.sleep(base_delay * (2**attempt))

    raise UnauthorizedException()


async def login_required(
    user_id: int = Depends(get_user_id),
    ijack_db: Session = Depends(get_ijack_db),
):
    """
    Check if the user is logged in and active.
    :param user_id: User ID.
    :param ijack_db: Database session.
    :return: User object.
    :raises UnauthorizedException: If the user is not logged in or active.
    """
    # Check if the user is logged in and active.
    # This function is used as a dependency in FastAPI routes.
    user_query = select(User.id, User.full_name, User.email).where(User.id == user_id)
    result = await ijack_db.execute(user_query)
    try:
        return result.scalar_one()
    except Exception:
        raise UnauthorizedException()


def customers_required(customer_ids: list[int] | int):
    """
    Check if the user belongs to the required customers.
    :param customer_ids: List of customer IDs or a single customer ID.
    :return: A function that checks if the user belongs to the required customers.
    """
    if isinstance(customer_ids, int):
        customer_ids = [customer_ids]

    async def belongs_to_customer(
        user_id: int = Depends(get_user_id),
        ijack_db: Session = Depends(get_ijack_db),
    ):
        subquery = (
            select(1)
            .where(
                user_customer_rel.c.user_id == user_id,
                user_customer_rel.c.customer_id.in_(customer_ids),
            )
            .exists()
        )
        # Just select the user ID without the selectinload option
        user_query = select(User.id).where(User.id == user_id, User.is_active, subquery)

        response = await ijack_db.execute(user_query)
        try:
            user_id = response.scalar_one()
            if not user_id:
                raise ForbiddenException()
            return user_id
        except Exception:
            raise ForbiddenException()

    return belongs_to_customer


def roles_required(roles: list[int] | int, rejected_roles: list[int] | None = None):
    """
    Check if the user has the required roles and doesn't have rejected roles.
    Mirrors Flask-Admin role_ids_accepted and role_ids_rejected functionality.
    :param roles: List of role IDs or a single role ID that are allowed.
    :param rejected_roles: List of role IDs that are not allowed.
    :return: A function that checks if the user has the required roles.
    """
    if isinstance(roles, int):
        roles = [roles]
    
    if rejected_roles is None:
        rejected_roles = []
    elif isinstance(rejected_roles, int):
        rejected_roles = [rejected_roles]

    async def has_roles(
        user_id: int = Depends(get_user_id),
        ijack_db: Session = Depends(get_ijack_db),
    ):
        # First check for rejected roles
        if rejected_roles:
            rejected_subquery = (
                select(1)
                .where(
                    user_role_rel.c.user_id == user_id,
                    user_role_rel.c.role_id.in_(rejected_roles),
                )
                .exists()
            )
            
            rejected_query = select(rejected_subquery)
            rejected_result = await ijack_db.execute(rejected_query)
            has_rejected_role = rejected_result.scalar()
            
            if has_rejected_role:
                raise ForbiddenException()
        
        # Then check for required roles
        subquery = (
            select(1)
            .where(
                user_role_rel.c.user_id == user_id,
                user_role_rel.c.role_id.in_(roles),
            )
            .exists()
        )

        # Just select the user ID without the selectinload option
        user_query = select(User.id).where(User.id == user_id, User.is_active, subquery)

        result = await ijack_db.execute(user_query)
        try:
            user_id = result.scalar_one()
            if not user_id:
                raise ForbiddenException()
            return user_id
        except Exception:
            raise ForbiddenException()

    return has_roles
