import React from "react";
import { createStore, SnapshotFromStore } from "@xstate/store";
import { useSelector } from "@xstate/store/react";

export const store = createStore({
  context: { 
    customers: [] as number[], 
    service_types: [] as number[], 
    service_date_from: null as Date|null, 
    service_date_to: null as Date|null, 
    selected_years: [] as number[],
    model_types: [] as (number|null)[],
    technicians: [] as number[],
    part_categories: [] as string[],
    selectedStructureId: null as number|null,
    selectedWorkOrderId: null as number|null,
    include_ijack: false as boolean,
    include_sales_parts: false as boolean,
  },
  on: {
    applyFilter: (context, event: { 
      customers?: number[], 
      service_types?: number[], 
      service_date_from?: Date|null, 
      service_date_to?: Date|null, 
      selected_years?: number[],
      model_types?: (number|null)[],
      technicians?: number[],
      part_categories?: string[],
      include_ijack?: boolean,
      include_sales_parts?: boolean
    }) => ({
      ...context,
      customers: event?.customers ?? context.customers,
      service_types: event?.service_types ?? context.service_types,
      service_date_from: event?.service_date_from === undefined ? context.service_date_from : event.service_date_from,
      service_date_to: event?.service_date_to === undefined ? context.service_date_to : event.service_date_to,
      selected_years: event?.selected_years ?? context.selected_years,
      model_types: event?.model_types ?? context.model_types,
      technicians: event?.technicians ?? context.technicians,
      part_categories: event?.part_categories ?? context.part_categories,
      include_ijack: event?.include_ijack ?? context.include_ijack,
      include_sales_parts: event?.include_sales_parts ?? context.include_sales_parts,
    }),
    clearFilters: (context) => ({
      ...context,
      customers: [],
      service_types: [],
      service_date_from: null,
      service_date_to: null,
      selected_years: [],
      model_types: [],
      technicians: [],
      part_categories: [],
      selectedStructureId: null,
      selectedWorkOrderId: null,
      include_ijack: false,
      include_sales_parts: false,
    }),
    selectStructure: (context, event: { structureId: number }) => ({
      ...context,
      selectedStructureId: event.structureId,
      selectedWorkOrderId: null, // Clear work order when selecting new structure
    }),
    selectWorkOrder: (context, event: { workOrderId: number }) => ({
      ...context,
      selectedWorkOrderId: event.workOrderId,
    }),
    clearStructureSelection: (context) => ({
      ...context,
      selectedStructureId: null,
      selectedWorkOrderId: null,
    }),
    clearWorkOrderSelection: (context) => ({
      ...context,
      selectedWorkOrderId: null,
    }),
  },
});

type ServiceCostStore = typeof store;

export const ServiceCostStoreContext = React.createContext<ServiceCostStore | null>(null);

export const useServiceCostSelector = <T>(selector: (state: SnapshotFromStore<ServiceCostStore>) => T) => {
  const store = React.useContext(ServiceCostStoreContext)
  if (!store) {
    throw new Error('Missing ServiceCostStoreProvider')
  }
  return useSelector(store, selector)
}

export const useServiceCostStore = () => {
  const store = React.useContext(ServiceCostStoreContext)
  if (!store) {
    throw new Error('Missing ServiceCostStoreProvider')
  }
  return store
}