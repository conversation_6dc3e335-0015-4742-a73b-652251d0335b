import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import { useServiceCostSelector, useServiceCostStore } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";
import { AgGridReact } from 'ag-grid-react';
import { ColDef, ValueFormatterParams } from 'ag-grid-community';
import { themeQuartz } from 'ag-grid-community';

const formatCurrency = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === '') return '';
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: 'CAD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(Number(params.value));
};

const formatDate = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === '') return '';
  return new Date(params.value).toLocaleDateString('en-CA');
};

const HighCostUnitsTable = () => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector(
    (state) => state.context.service_types,
  );
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const store = useServiceCostStore();
  
  const { data, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/high-cost-units",
    {
      body: {
        customers,
        models,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
      },
    },
  );

  const columnDefs: ColDef[] = [
    { 
      field: 'structure_number', 
      headerName: 'Structure #', 
      pinned: 'left',
      width: 150,
      cellStyle: { fontWeight: 'bold' },
      cellRenderer: (params: any) => {
        return (
          <div 
            className="cursor-pointer text-blue-600 hover:text-blue-800 hover:underline"
            onClick={() => store.send({ type: 'selectStructure', structureId: params.data.structure_id })}
            title="Click to view work orders for this structure"
          >
            {params.value}
          </div>
        );
      }
    },
    { 
      field: 'power_unit', 
      headerName: 'Power Unit',
      width: 140
    },
    { 
      field: 'model_name', 
      headerName: 'Model',
      width: 120
    },
    { 
      field: 'customer_name', 
      headerName: 'Customer',
      width: 180
    },
    { 
      field: 'total_cost', 
      headerName: 'Total Cost', 
      valueFormatter: formatCurrency,
      width: 120,
      type: 'numericColumn',
      sort: 'desc'
    },
    { 
      field: 'work_order_count', 
      headerName: '# Services',
      width: 100,
      type: 'numericColumn'
    },
    { 
      field: 'avg_cost_per_service', 
      headerName: 'Avg Cost/Service',
      valueFormatter: formatCurrency,
      width: 140,
      type: 'numericColumn'
    },
    { 
      field: 'last_service_date', 
      headerName: 'Last Service',
      valueFormatter: formatDate,
      width: 120
    },
    { 
      field: 'cost_trend', 
      headerName: 'Trend',
      width: 100,
      cellRenderer: (params: any) => {
        const trend = params.value;
        if (trend === 'increasing') {
          return <span className="text-red-600 font-medium">↗ Increasing</span>;
        } else if (trend === 'decreasing') {
          return <span className="text-green-600 font-medium">↘ Decreasing</span>;
        } else {
          return <span className="text-gray-500">→ Stable</span>;
        }
      }
    },
  ];

  const defaultColDef = {
    sortable: true,
    filter: true,
    resizable: true,
  };

  return (
    <Card className="col-span-full">
      <CardHeader className="pb-2">
        <CardTitle>High-Cost Units</CardTitle>
        <CardDescription>
          Units with the highest total service costs
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
            <Skeleton className="h-64 w-full" />
          </div>
        ) : isError ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">Error loading table data</p>
          </div>
        ) : !data?.result || data.result.length === 0 ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">No high-cost units found</p>
          </div>
        ) : (
          <div className="h-[500px] w-full">
            <AgGridReact
              theme={themeQuartz}
              rowModelType="clientSide"
              rowData={data.result}
              columnDefs={columnDefs}
              defaultColDef={defaultColDef}
              pagination={true}
              paginationPageSize={20}
              paginationPageSizeSelector={[10, 20, 50, 100]}
              domLayout="normal"
              suppressMenuHide={true}
              enableCellTextSelection={true}
              animateRows={true}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export { HighCostUnitsTable };