import { CogIcon, Users, WrenchIcon, HardHat, Tag } from "lucide-react";
import FilterMenu from "../ui/filter-menu";
import { $api } from "@/api/web-api";
import { useServiceCostSelector, useServiceCostStore } from "@/stores/service-costs/hooks";
import { Small } from "@/components/ui/typography";
import { Skeleton } from "../ui/skeleton";
import CalendarFilterMenu from "../ui/calendar-filter-menu";
import { Checkbox } from "../ui/checkbox";

function CustomerFilter() {
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "get",
    "/v1/customer/",
  );
  const storekeys = useServiceCostSelector((state) => state.context.customers);
  const includeIjack = useServiceCostSelector((state) => state.context.include_ijack);
  const store = useServiceCostStore();
  
  const setKeys = (keys: number[]) => {
    store.send({ type: "applyFilter", customers: keys });
  };
  
  const handleToggle = (checked: boolean) => {
    store.send({ type: "applyFilter", include_ijack: checked });
  };

  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Customers:</Small>
      <div className="flex items-center space-x-2 mt-1 mb-2">
        <Checkbox
          id="include-ijack"
          checked={includeIjack}
          onCheckedChange={handleToggle}
        />
        <label
          htmlFor="include-ijack"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          Include IJACK
        </label>
      </div>
      {isSuccess ? (
        <FilterMenu
          options={data.result}
          keyGetter={(item) => item.id}
          labelGetter={(item) => item.customer}
          selectedKeys={storekeys}
          setSelectedKeys={setKeys}
          placeholder="Search customers..."
          allSelectedText="All customers"
          noneSelectedText="All customers"
          multipleSelectedText={(selectedKeys) =>
            `${selectedKeys.length} customers selected`
          }
          emptyWhenAllSelected={true}
          icon={<Users strokeWidth={2} className="h-4 w-4" />}
        />
      ) : isLoading ? (
        <Skeleton className="h-8 w-full bg-gray-200" />
      ) : isError ? (
        "Error loading customers"
      ) : null}
    </div>
  );
}


function ServiceTypeFilter() {
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "get",
    "/v1/work-order/service-type",
  );
  const storekeys = useServiceCostSelector((state) => state.context.service_types);
  const store = useServiceCostStore();
  const setKeys = (keys: number[]) => {
    store.send({ type: "applyFilter", service_types: keys });
  };
  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Service Types:</Small>
      {isSuccess ? (
        <FilterMenu
          options={data.result}
          keyGetter={(item) => item.id}
          labelGetter={(item) => item.name}
          selectedKeys={storekeys}
          setSelectedKeys={setKeys}
          placeholder="Search service types..."
          allSelectedText="All service types"
          noneSelectedText="All service types"
          multipleSelectedText={(selectedKeys) =>
            `${selectedKeys.length} service types selected`
          }
          emptyWhenAllSelected={true}
          icon={<WrenchIcon strokeWidth={2} className="h-4 w-4" />}
        />
      ) : isLoading ? (
        <Skeleton className="h-8 w-full bg-gray-200" />
      ) : isError ? (
        "Error loading service types"
      ) : null}
    </div>
  );
}

function ModelTypeFilter() {
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "get",
    "/v1/structure/model",
  );
  const storekeys = useServiceCostSelector((state) => state.context.model_types);
  const store = useServiceCostStore();
  const setKeys = (keys: (number | null)[]) => {
    store.send({ type: "applyFilter", model_types: keys });
  };
  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Equipment Serviced:</Small>
      {isSuccess ? (
        <FilterMenu
          options={data.result}
          keyGetter={(item) => item.id}
          labelGetter={(item) => item.model}
          selectedKeys={storekeys}
          setSelectedKeys={setKeys}
          placeholder="Search model types..."
          nullable
          allSelectedText="All model types"
          noneSelectedText="All model types"
          multipleSelectedText={(selectedKeys) =>
            `${selectedKeys.length} model types selected`
          }
          emptyWhenAllSelected={true}
          icon={<CogIcon strokeWidth={2} className="h-4 w-4" />}
        />
      ) : isLoading ? (
        <Skeleton className="h-8 w-full bg-gray-200" />
      ) : isError ? (
        "Error loading model types"
      ) : null}
    </div>
  );
}

function TechnicianFilter() {
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "get",
    "/v1/api/technicians/",
  );
  const storekeys = useServiceCostSelector((state) => state.context.technicians);
  const store = useServiceCostStore();
  const setKeys = (keys: number[]) => {
    store.send({ type: "applyFilter", technicians: keys });
  };

  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Technicians:</Small>
      {isSuccess ? (
        <FilterMenu
          options={data.result}
          keyGetter={(item) => item.id}
          labelGetter={(item) => item.full_name}
          selectedKeys={storekeys}
          setSelectedKeys={setKeys}
          placeholder="Search technicians..."
          allSelectedText="All technicians"
          noneSelectedText="All technicians"
          multipleSelectedText={(selectedKeys) =>
            `${selectedKeys.length} technicians selected`
          }
          emptyWhenAllSelected={true}
          icon={<HardHat strokeWidth={2} className="h-4 w-4" />}
        />
      ) : isLoading ? (
        <Skeleton className="h-8 w-full bg-gray-200" />
      ) : isError ? (
        "Error loading technicians"
      ) : null}
    </div>
  );
}

function PartCategoryFilter() {
  const storekeys = useServiceCostSelector((state) => state.context.part_categories);
  const includeSalesParts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const store = useServiceCostStore();
  const setKeys = (keys: string[]) => {
    store.send({ type: "applyFilter", part_categories: keys });
  };
  
  const handleSalesPartsToggle = (checked: boolean) => {
    store.send({ type: "applyFilter", include_sales_parts: checked });
  };

  // Static part categories based on the system
  const partCategories = [
    { id: "050 (Sales)", name: "050 (Sales)" },
    { id: "060 (PM Parts)", name: "060 (PM Parts)" },
    { id: "070 (Service)", name: "070 (Service)" },
    { id: "0", name: "0" },
    { id: "Other", name: "Other" },
  ];

  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Part Categories:</Small>
      <div className="flex items-center space-x-2 mt-1 mb-2">
        <Checkbox
          id="include-sales-parts"
          checked={includeSalesParts}
          onCheckedChange={handleSalesPartsToggle}
        />
        <label
          htmlFor="include-sales-parts"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          Include Sales Parts
        </label>
      </div>
      <FilterMenu
        options={partCategories}
        keyGetter={(item) => item.id}
        labelGetter={(item) => item.name}
        selectedKeys={storekeys}
        setSelectedKeys={setKeys}
        placeholder="Search part categories..."
        allSelectedText="All categories"
        noneSelectedText="All categories"
        multipleSelectedText={(selectedKeys) =>
          `${selectedKeys.length} categories selected`
        }
        emptyWhenAllSelected={true}
        icon={<Tag strokeWidth={2} className="h-4 w-4" />}
      />
    </div>
  );
}

function YearSelector() {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const store = useServiceCostStore();

  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/available-years",
    {
      body: {
        customers,
        service_types,
        models,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
      },
    },
  );

  const handleYearToggle = (year: number, checked: boolean) => {
    const updatedYears = checked
      ? [...selected_years, year]
      : selected_years.filter(y => y !== year);
    
    store.send({ 
      type: "applyFilter", 
      selected_years: updatedYears.sort((a, b) => b - a) // Sort descending
    });
  };

  if (isLoading) {
    return <Skeleton className="h-6 w-full bg-gray-200" />;
  }

  if (isError || !isSuccess || !data?.result?.years?.length) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-2 mt-1 mb-2">
      {data.result.years.sort((a, b) => a - b).map((year) => (
        <div key={year} className="flex items-center space-x-1">
          <Checkbox
            id={`year-${year}`}
            checked={selected_years.includes(year)}
            onCheckedChange={(checked) => handleYearToggle(year, checked as boolean)}
          />
          <label
            htmlFor={`year-${year}`}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {year}
          </label>
        </div>
      ))}
    </div>
  );
}

function ServiceDateFilter() {
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const store = useServiceCostStore();
  
  const setServiceDates = (dateRange: {
    from: Date | null;
    to: Date | null;
  }) => {
    // When user sets date range, clear selected years
    store.send({
      type: "applyFilter",
      service_date_from: dateRange.from,
      service_date_to: dateRange.to,
      selected_years: [], // Clear years when using date picker
    });
  };

  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Service Dates:</Small>
      <YearSelector />
      <CalendarFilterMenu
        onChange={(range) => setServiceDates(range)}
        range={{ from, to }}
        noneSelectedText="All service dates"
        disabled={selected_years.length > 0} // Disable when years are selected
      />
    </div>
  );
}

function ResetFilters() {
  const store = useServiceCostStore();
  const clearFilters = () => {
    store.send({ type: "clearFilters" });
  };
  return (
    <button
      className="flex items-center gap-2 rounded-md border border-gray-300 bg-white px-2 py-1 text-sm text-gray-700 hover:bg-gray-50"
      onClick={clearFilters}
    >
      Reset Filters
    </button>
  );
}

export {
  CustomerFilter,
  ModelTypeFilter,
  ServiceTypeFilter,
  TechnicianFilter,
  PartCategoryFilter,
  ServiceDateFilter,
  ResetFilters,
};